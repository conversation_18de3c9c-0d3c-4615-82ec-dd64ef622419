// Ad Network Configuration
export interface AdNetwork {
  name: string;
  appId: string;
  adUnits: {
    banner: string;
    rectangle: string;
    interstitial: string;
    appOpen: string;
    rewarded: string;
    native: string;
  };
}

// PubScale Company (Bangalore)
export const PUBSCALE_NETWORK: AdNetwork = {
  name: 'PubScale',
  appId: 'ca-app-pub-3206456546664189~6654042212',
  adUnits: {
    banner: '/22387492205,23297313686/com.adtip.app.adtip_app.Banner0.1752230666',
    rectangle: '/22387492205,23297313686/com.adtip.app.adtip_app.Mrec0.1752230666',
    interstitial: '/22387492205,23297313686/com.adtip.app.adtip_app.Interstitial0.1752230772',
    appOpen: '/22387492205,23297313686/com.adtip.app.adtip_app.AppOpen0.1752230585',
    rewarded: '/22387492205,23297313686/com.adtip.app.adtip_app.Rewarded0.1752230221',
    native: '/22387492205,23297313686/com.adtip.app.adtip_app.Native0.1752230236',
  },
};

// Only Pubscale network, no rotation
const networks = [PUBSCALE_NETWORK];
let currentNetworkIndex = 0;

class AdRotationService {
  private static instance: AdRotationService;

  static getInstance(): AdRotationService {
    if (!AdRotationService.instance) {
      AdRotationService.instance = new AdRotationService();
    }
    return AdRotationService.instance;
  }

  /**
   * Always return Pubscale as the current network
   */
  getCurrentNetwork(): AdNetwork {
    return networks[0];
  }

  /**
   * No-op: Always return Pubscale as the next network
   */
  getNextNetwork(): AdNetwork {
    return networks[0];
  }

  /**
   * Get ad unit ID for specific ad type (always Pubscale)
   */
  getAdUnitId(adType: keyof AdNetwork['adUnits']): string {
    const network = this.getCurrentNetwork();
    const adUnitId = network.adUnits[adType];
    console.log(`📱 [AdRotation] Using ${network.name} ${adType} ad: ${adUnitId}`);
    return adUnitId;
  }

  /**
   * No-op: Always return Pubscale ad unit ID
   */
  getNextAdUnitId(adType: keyof AdNetwork['adUnits']): string {
    return this.getAdUnitId(adType);
  }

  /**
   * Get current network name (always Pubscale)
   */
  getCurrentNetworkName(): string {
    return networks[0].name;
  }

  /**
   * Get rotation statistics (no rotation)
   */
  getRotationStats() {
    return {
      currentNetwork: networks[0].name,
      totalRotations: 0,
      availableNetworks: 1,
    };
  }

  /**
   * No-op: Only one network
   */
  switchToNetwork(networkIndex: number): AdNetwork {
    return networks[0];
  }

  /**
   * Get all available networks (only Pubscale)
   */
  getAllNetworks(): AdNetwork[] {
    return networks;
  }
}

export default AdRotationService; 