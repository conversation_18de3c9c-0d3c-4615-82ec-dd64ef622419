import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { BannerAd, BannerAdSize, TestIds } from 'react-native-google-mobile-ads';
// import AdRotationService from '../services/AdRotationService';

// Test Ad Unit ID (for development/testing)
const TEST_RECTANGLE_AD_UNIT_ID = TestIds.BANNER; // Official Google test ID for banner ads

// Use only Pubscale ad unit
const PUBSCALE_RECTANGLE_AD_UNIT_ID = '/22387492205,23297313686/com.adtip.app.adtip_app.Mrec0.1752230666';

const getRectangleAdUnitId = () => {
  if (__DEV__) {
    return TEST_RECTANGLE_AD_UNIT_ID;
  }
  return PUBSCALE_RECTANGLE_AD_UNIT_ID;
};

const RectangleAdComponent = () => {
  const [adFailed, setAdFailed] = useState(false);

  const handleAdFailed = (error: any) => {
    console.log('Rectangle ad failed to load:', error);
    setAdFailed(true);
  };

  const handleAdLoaded = () => {
    console.log('Rectangle ad loaded successfully');
    setAdFailed(false);
  };

  return (
    <View style={styles.container}>
      <BannerAd
        unitId={getRectangleAdUnitId()}
        size={BannerAdSize.MEDIUM_RECTANGLE}
        requestOptions={{
          requestNonPersonalizedAdsOnly: false,
          keywords: ['entertainment', 'social', 'communication', 'lifestyle'],
          contentUrl: 'https://adtip.app',
        }}
        onAdLoaded={handleAdLoaded}
        onAdFailedToLoad={handleAdFailed}
        onAdOpened={() => {
          console.log('Rectangle ad opened');
        }}
        onAdClosed={() => {
          console.log('Rectangle ad closed');
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginVertical: 12,
  },
});

export default RectangleAdComponent; 