<dependencies>
  <compile
      roots=":@@:react-native-community_datetimepicker::release,:@@:react-native-google-mobile-ads::release,:@@:react-native-permissions::release,:@@:react-native-screens::release,:@@:react-native-video::release,:@@:react-native-vision-camera::release,:@@:notifee_react-native::release,:@@:react-native-firebase_analytics::release,:@@:react-native-firebase_auth::release,:@@:react-native-firebase_crashlytics::release,:@@:react-native-firebase_database::release,:@@:react-native-firebase_firestore::release,:@@:react-native-firebase_messaging::release,:@@:react-native-firebase_storage::release,:@@:react-native-firebase_app::release,:@@:react-native-pager-view::release,:@@:react-native-restart::release,com.facebook.react:react-android:0.79.2:release@aar,com.pubscale.sdkone:offerwall:1.0.11@aar,com.facebook.fresco:fresco:3.6.0@aar,com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar,com.facebook.fresco:middleware:3.6.0@aar,com.facebook.fresco:ui-common:3.6.0@aar,com.google.firebase:firebase-analytics:22.4.0@aar,com.google.android.gms:play-services-measurement-api:22.4.0@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,androidx.appcompat:appcompat:1.7.0@aar,com.google.android.ump:user-messaging-platform:3.2.0@aar,com.google.android.gms:play-services-measurement:22.4.0@aar,com.google.android.gms:play-services-measurement-sdk:22.4.0@aar,com.google.firebase:firebase-messaging:24.1.1@aar,com.google.firebase:firebase-iid-interop:17.1.0@aar,com.google.firebase:firebase-measurement-connector:20.0.1@aar,com.google.android.gms:play-services-measurement-impl:22.4.0@aar,com.google.android.gms:play-services-ads-identifier:18.0.1@aar,com.google.android.gms:play-services-measurement-sdk-api:22.4.0@aar,com.google.android.gms:play-services-measurement-base:22.4.0@aar,com.google.android.gms:play-services-stats:17.0.2@aar,com.google.firebase:firebase-installations:18.0.0@aar,com.google.firebase:firebase-installations-interop:17.2.0@aar,com.google.firebase:firebase-common-ktx:21.0.0@aar,com.google.firebase:firebase-common:21.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.1.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.2.0-alpha01@aar,com.google.android.exoplayer:exoplayer:2.19.1@aar,com.google.android.exoplayer:exoplayer-ui:2.19.1@aar,androidx.media:media:1.7.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.core:core-ktx:1.16.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,com.google.android.gms:play-services-base:18.5.0@aar,androidx.core:core:1.16.0@aar,androidx.core:core:1.16.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar,androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0@jar,androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11@aar,androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.9.0@jar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.android.gms:play-services-basement:18.5.0@aar,androidx.fragment:fragment:1.6.1@aar,androidx.fragment:fragment:1.6.1@aar,androidx.activity:activity:1.10.1@aar,androidx.savedstate:savedstate:1.2.1@aar,com.facebook.fresco:fbcore:3.6.0@aar,com.facebook.fresco:drawee:3.6.0@aar,com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.google.firebase:firebase-encoders-json:18.0.1@aar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.firebase:firebase-encoders:17.0.0@jar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-jvm:1.4.2@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.print:print:1.0.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.annotation:annotation-jvm:1.8.1@jar,androidx.core:core-viewtree:1.0.0@aar,androidx.annotation:annotation-experimental:1.4.1@aar,com.facebook.fresco:ui-core:3.6.0@aar,com.facebook.fresco:imagepipeline:3.6.0@aar,com.facebook.fresco:imagepipeline-base:3.6.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,:@@:d11_react-native-fast-image::release,:@@:nozbe_watermelondb::release,:@@:react-native-async-storage_async-storage::release,:@@:react-native-clipboard_clipboard::release,:@@:react-native-community_blur::release,:@@:react-native-community_masked-view::release,:@@:react-native-community_netinfo::release,:@@:shopify_react-native-skia::release,:@@:videosdk.live_react-native-webrtc::release,:@@:lottie-react-native::release,:@@:react-native-callkeep::release,:@@:react-native-compressor::release,:@@:react-native-create-thumbnail::release,:@@:react-native-date-picker::release,:@@:react-native-fs::release,:@@:react-native-geolocation-service::release,:@@:react-native-gesture-handler::release,:@@:react-native-get-random-values::release,:@@:react-native-image-crop-picker::release,:@@:react-native-image-picker::release,:@@:react-native-linear-gradient::release,:@@:react-native-orientation-locker::release,:@@:react-native-razorpay::release,:@@:react-native-reanimated::release,:@@:react-native-safe-area-context::release,:@@:react-native-svg::release,:@@:react-native-vector-icons::release,:@@:react-native-view-shot::release,:@@:react-native-webview::release,:@@:rnincallmanager::release,com.google.android.exoplayer:exoplayer-core:2.19.1@aar,com.google.android.exoplayer:exoplayer-hls:2.19.1@aar,com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1@aar,com.google.android.exoplayer:exoplayer-dash:2.19.1@aar,com.facebook.react:hermes-android:0.79.2:release@aar,org.jetbrains:annotations:23.0.0@jar,com.google.android.exoplayer:exoplayer-common:2.19.1@aar,com.google.guava:guava:33.3.1-android@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,androidx.tracing:tracing:1.2.0@aar,org.jspecify:jspecify:1.0.0@jar,androidx.autofill:autofill:1.1.0@aar,com.facebook.fbjni:fbjni:0.7.0@aar,com.facebook.soloader:soloader:0.12.1@aar,com.facebook.soloader:nativeloader:0.12.1@jar,com.facebook.soloader:annotation:0.12.1@jar,com.facebook.infer.annotation:infer-annotation:0.18.0@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar,com.facebook.fresco:imagepipeline-native:3.6.0@aar,com.facebook.fresco:memory-type-ashmem:3.6.0@aar,com.facebook.fresco:memory-type-native:3.6.0@aar,com.facebook.fresco:memory-type-java:3.6.0@aar,com.facebook.fresco:nativeimagefilters:3.6.0@aar,com.facebook.fresco:nativeimagetranscoder:3.6.0@aar,com.facebook.yoga:proguard-annotations:1.19.0@jar,com.google.firebase:firebase-components:18.0.0@aar,com.google.firebase:firebase-annotations:16.2.0@jar,javax.inject:javax.inject:1@jar,com.google.guava:failureaccess:1.0.2@jar,org.checkerframework:checker-qual:3.43.0@jar,com.google.errorprone:error_prone_annotations:2.28.0@jar,com.google.j2objc:j2objc-annotations:3.0.0@jar,com.google.firebase:firebase-datatransport:19.0.0@aar,com.google.android.exoplayer:exoplayer-database:2.19.1@aar,com.google.android.exoplayer:exoplayer-datasource:2.19.1@aar,com.google.android.exoplayer:exoplayer-decoder:2.19.1@aar,com.google.android.exoplayer:exoplayer-extractor:2.19.1@aar,com.google.android.exoplayer:exoplayer-container:2.19.1@aar,com.google.android.exoplayer:exoplayer-rtsp:2.19.1@aar">
    <dependency
        name=":@@:react-native-community_datetimepicker::release"
        simpleName="artifacts::react-native-community_datetimepicker"/>
    <dependency
        name=":@@:react-native-google-mobile-ads::release"
        simpleName="artifacts::react-native-google-mobile-ads"/>
    <dependency
        name=":@@:react-native-permissions::release"
        simpleName="artifacts::react-native-permissions"/>
    <dependency
        name=":@@:react-native-screens::release"
        simpleName="artifacts::react-native-screens"/>
    <dependency
        name=":@@:react-native-video::release"
        simpleName="artifacts::react-native-video"/>
    <dependency
        name=":@@:react-native-vision-camera::release"
        simpleName="artifacts::react-native-vision-camera"/>
    <dependency
        name=":@@:notifee_react-native::release"
        simpleName="artifacts::notifee_react-native"/>
    <dependency
        name=":@@:react-native-firebase_analytics::release"
        simpleName="artifacts::react-native-firebase_analytics"/>
    <dependency
        name=":@@:react-native-firebase_auth::release"
        simpleName="artifacts::react-native-firebase_auth"/>
    <dependency
        name=":@@:react-native-firebase_crashlytics::release"
        simpleName="artifacts::react-native-firebase_crashlytics"/>
    <dependency
        name=":@@:react-native-firebase_database::release"
        simpleName="artifacts::react-native-firebase_database"/>
    <dependency
        name=":@@:react-native-firebase_firestore::release"
        simpleName="artifacts::react-native-firebase_firestore"/>
    <dependency
        name=":@@:react-native-firebase_messaging::release"
        simpleName="artifacts::react-native-firebase_messaging"/>
    <dependency
        name=":@@:react-native-firebase_storage::release"
        simpleName="artifacts::react-native-firebase_storage"/>
    <dependency
        name=":@@:react-native-firebase_app::release"
        simpleName="artifacts::react-native-firebase_app"/>
    <dependency
        name=":@@:react-native-pager-view::release"
        simpleName="artifacts::react-native-pager-view"/>
    <dependency
        name=":@@:react-native-restart::release"
        simpleName="artifacts::react-native-restart"/>
    <dependency
        name="com.facebook.react:react-android:0.79.2:release@aar"
        simpleName="com.facebook.react:react-android"/>
    <dependency
        name="com.pubscale.sdkone:offerwall:1.0.11@aar"
        simpleName="com.pubscale.sdkone:offerwall"/>
    <dependency
        name="com.facebook.fresco:fresco:3.6.0@aar"
        simpleName="com.facebook.fresco:fresco"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-okhttp3"/>
    <dependency
        name="com.facebook.fresco:middleware:3.6.0@aar"
        simpleName="com.facebook.fresco:middleware"/>
    <dependency
        name="com.facebook.fresco:ui-common:3.6.0@aar"
        simpleName="com.facebook.fresco:ui-common"/>
    <dependency
        name="com.google.firebase:firebase-analytics:22.4.0@aar"
        simpleName="com.google.firebase:firebase-analytics"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-api:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-api"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.google.android.ump:user-messaging-platform:3.2.0@aar"
        simpleName="com.google.android.ump:user-messaging-platform"/>
    <dependency
        name="com.google.android.gms:play-services-measurement:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk"/>
    <dependency
        name="com.google.firebase:firebase-messaging:24.1.1@aar"
        simpleName="com.google.firebase:firebase-messaging"/>
    <dependency
        name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-iid-interop"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:20.0.1@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-impl:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-impl"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:18.0.1@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="com.google.firebase:firebase-installations:18.0.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.2.0@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-common:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.1.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.2.0-alpha01@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-ui:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-ui"/>
    <dependency
        name="androidx.media:media:1.7.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.core:core-ktx:1.16.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="androidx.core:core:1.16.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices-java"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.9.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.6.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.10.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="com.facebook.fresco:fbcore:3.6.0@aar"
        simpleName="com.facebook.fresco:fbcore"/>
    <dependency
        name="com.facebook.fresco:drawee:3.6.0@aar"
        simpleName="com.facebook.fresco:drawee"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp-urlconnection"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.1@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.2@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="com.facebook.fresco:ui-core:3.6.0@aar"
        simpleName="com.facebook.fresco:ui-core"/>
    <dependency
        name="com.facebook.fresco:imagepipeline:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-base:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-base"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name=":@@:d11_react-native-fast-image::release"
        simpleName="artifacts::d11_react-native-fast-image"/>
    <dependency
        name=":@@:nozbe_watermelondb::release"
        simpleName="artifacts::nozbe_watermelondb"/>
    <dependency
        name=":@@:react-native-async-storage_async-storage::release"
        simpleName="artifacts::react-native-async-storage_async-storage"/>
    <dependency
        name=":@@:react-native-clipboard_clipboard::release"
        simpleName="artifacts::react-native-clipboard_clipboard"/>
    <dependency
        name=":@@:react-native-community_blur::release"
        simpleName="artifacts::react-native-community_blur"/>
    <dependency
        name=":@@:react-native-community_masked-view::release"
        simpleName="artifacts::react-native-community_masked-view"/>
    <dependency
        name=":@@:react-native-community_netinfo::release"
        simpleName="artifacts::react-native-community_netinfo"/>
    <dependency
        name=":@@:shopify_react-native-skia::release"
        simpleName="artifacts::shopify_react-native-skia"/>
    <dependency
        name=":@@:videosdk.live_react-native-webrtc::release"
        simpleName="artifacts::videosdk.live_react-native-webrtc"/>
    <dependency
        name=":@@:lottie-react-native::release"
        simpleName="artifacts::lottie-react-native"/>
    <dependency
        name=":@@:react-native-callkeep::release"
        simpleName="artifacts::react-native-callkeep"/>
    <dependency
        name=":@@:react-native-compressor::release"
        simpleName="artifacts::react-native-compressor"/>
    <dependency
        name=":@@:react-native-create-thumbnail::release"
        simpleName="artifacts::react-native-create-thumbnail"/>
    <dependency
        name=":@@:react-native-date-picker::release"
        simpleName="artifacts::react-native-date-picker"/>
    <dependency
        name=":@@:react-native-fs::release"
        simpleName="artifacts::react-native-fs"/>
    <dependency
        name=":@@:react-native-geolocation-service::release"
        simpleName="artifacts::react-native-geolocation-service"/>
    <dependency
        name=":@@:react-native-gesture-handler::release"
        simpleName="artifacts::react-native-gesture-handler"/>
    <dependency
        name=":@@:react-native-get-random-values::release"
        simpleName="artifacts::react-native-get-random-values"/>
    <dependency
        name=":@@:react-native-image-crop-picker::release"
        simpleName="artifacts::react-native-image-crop-picker"/>
    <dependency
        name=":@@:react-native-image-picker::release"
        simpleName="artifacts::react-native-image-picker"/>
    <dependency
        name=":@@:react-native-linear-gradient::release"
        simpleName="artifacts::react-native-linear-gradient"/>
    <dependency
        name=":@@:react-native-orientation-locker::release"
        simpleName="artifacts::react-native-orientation-locker"/>
    <dependency
        name=":@@:react-native-razorpay::release"
        simpleName="artifacts::react-native-razorpay"/>
    <dependency
        name=":@@:react-native-reanimated::release"
        simpleName="artifacts::react-native-reanimated"/>
    <dependency
        name=":@@:react-native-safe-area-context::release"
        simpleName="artifacts::react-native-safe-area-context"/>
    <dependency
        name=":@@:react-native-svg::release"
        simpleName="artifacts::react-native-svg"/>
    <dependency
        name=":@@:react-native-vector-icons::release"
        simpleName="artifacts::react-native-vector-icons"/>
    <dependency
        name=":@@:react-native-view-shot::release"
        simpleName="artifacts::react-native-view-shot"/>
    <dependency
        name=":@@:react-native-webview::release"
        simpleName="artifacts::react-native-webview"/>
    <dependency
        name=":@@:rnincallmanager::release"
        simpleName="artifacts::rnincallmanager"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-core:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-core"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-hls:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-hls"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-smoothstreaming"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-dash:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-dash"/>
    <dependency
        name="com.facebook.react:hermes-android:0.79.2:release@aar"
        simpleName="com.facebook.react:hermes-android"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-common:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-common"/>
    <dependency
        name="com.google.guava:guava:33.3.1-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="androidx.autofill:autofill:1.1.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="com.facebook.fbjni:fbjni:0.7.0@aar"
        simpleName="com.facebook.fbjni:fbjni"/>
    <dependency
        name="com.facebook.soloader:soloader:0.12.1@aar"
        simpleName="com.facebook.soloader:soloader"/>
    <dependency
        name="com.facebook.soloader:nativeloader:0.12.1@jar"
        simpleName="com.facebook.soloader:nativeloader"/>
    <dependency
        name="com.facebook.soloader:annotation:0.12.1@jar"
        simpleName="com.facebook.soloader:annotation"/>
    <dependency
        name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
        simpleName="com.facebook.infer.annotation:infer-annotation"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
        simpleName="org.jetbrains.kotlin:kotlin-annotations-jvm"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-native:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-ashmem:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-ashmem"/>
    <dependency
        name="com.facebook.fresco:memory-type-native:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-java:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-java"/>
    <dependency
        name="com.facebook.fresco:nativeimagefilters:3.6.0@aar"
        simpleName="com.facebook.fresco:nativeimagefilters"/>
    <dependency
        name="com.facebook.fresco:nativeimagetranscoder:3.6.0@aar"
        simpleName="com.facebook.fresco:nativeimagetranscoder"/>
    <dependency
        name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
        simpleName="com.facebook.yoga:proguard-annotations"/>
    <dependency
        name="com.google.firebase:firebase-components:18.0.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.2@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="org.checkerframework:checker-qual:3.43.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.28.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="com.google.j2objc:j2objc-annotations:3.0.0@jar"
        simpleName="com.google.j2objc:j2objc-annotations"/>
    <dependency
        name="com.google.firebase:firebase-datatransport:19.0.0@aar"
        simpleName="com.google.firebase:firebase-datatransport"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-database:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-database"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-datasource:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-datasource"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-decoder:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-decoder"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-extractor:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-extractor"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-container:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-container"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-rtsp:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-rtsp"/>
  </compile>
  <package
      roots=":@@:react-native-community_datetimepicker::release,:@@:lottie-react-native::release,:@@:react-native-compressor::release,:@@:react-native-gesture-handler::release,:@@:react-native-google-mobile-ads::release,:@@:react-native-pager-view::release,:@@:react-native-permissions::release,:@@:react-native-safe-area-context::release,:@@:react-native-screens::release,:@@:react-native-video::release,:@@:react-native-vision-camera::release,:@@:react-native-webview::release,:@@:d11_react-native-fast-image::release,:@@:notifee_react-native::release,:@@:nozbe_watermelondb::release,:@@:react-native-async-storage_async-storage::release,:@@:react-native-clipboard_clipboard::release,:@@:react-native-community_blur::release,:@@:react-native-community_masked-view::release,:@@:react-native-community_netinfo::release,:@@:react-native-firebase_analytics::release,:@@:react-native-firebase_auth::release,:@@:react-native-firebase_crashlytics::release,:@@:react-native-firebase_database::release,:@@:react-native-firebase_firestore::release,:@@:react-native-firebase_messaging::release,:@@:react-native-firebase_storage::release,:@@:react-native-firebase_app::release,:@@:shopify_react-native-skia::release,:@@:videosdk.live_react-native-webrtc::release,:@@:react-native-callkeep::release,:@@:react-native-create-thumbnail::release,:@@:react-native-date-picker::release,:@@:react-native-fs::release,:@@:react-native-geolocation-service::release,:@@:react-native-get-random-values::release,:@@:react-native-image-crop-picker::release,:@@:react-native-image-picker::release,:@@:react-native-linear-gradient::release,:@@:react-native-orientation-locker::release,:@@:react-native-razorpay::release,:@@:react-native-reanimated::release,:@@:react-native-restart::release,:@@:react-native-svg::release,:@@:react-native-vector-icons::release,:@@:react-native-view-shot::release,:@@:rnincallmanager::release,com.facebook.react:react-android:0.79.2:release@aar,com.pubscale.sdkone:offerwall:1.0.11@aar,com.google.firebase:firebase-messaging:24.1.1@aar,com.github.banketree:AndroidLame-kotlin:v0.0.1@aar,com.razorpay:checkout:1.6.41@aar,com.google.android.material:material:1.12.0@aar,com.razorpay:standard-core:1.6.53@aar,com.pubscale.caterpillar:analytics:0.23@aar,com.squareup.okhttp3:logging-interceptor:4.10.0@jar,com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar,com.google.firebase:firebase-crashlytics-ndk:19.4.4@aar,com.google.firebase:firebase-crashlytics:19.4.4@aar,com.google.firebase:firebase-analytics:22.4.0@aar,com.google.android.gms:play-services-measurement-api:22.4.0@aar,com.google.firebase:firebase-auth:23.2.1@aar,com.google.firebase:firebase-database:21.0.0@aar,com.google.firebase:firebase-firestore:25.1.4@aar,com.google.firebase:firebase-storage:21.0.2@aar,com.google.firebase:firebase-sessions:2.1.2@aar,com.google.firebase:firebase-installations:18.0.0@aar,com.google.firebase:firebase-appcheck:18.0.0@aar,com.google.firebase:firebase-common-ktx:21.0.0@aar,com.github.bumptech.glide:okhttp3-integration:4.14.2@aar,com.github.yalantis:ucrop:2.2.10@aar,com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar,androidx.media3:media3-exoplayer-smoothstreaming:1.4.1@aar,androidx.media3:media3-exoplayer-dash:1.4.1@aar,androidx.media3:media3-exoplayer-hls:1.4.1@aar,androidx.media3:media3-exoplayer:1.4.1@aar,androidx.media3:media3-ui:1.4.1@aar,androidx.media3:media3-session:1.4.1@aar,androidx.media3:media3-datasource:1.4.1@aar,androidx.media3:media3-extractor:1.4.1@aar,androidx.media3:media3-container:1.4.1@aar,androidx.media3:media3-decoder:1.4.1@aar,androidx.media3:media3-database:1.4.1@aar,androidx.media3:media3-common:1.4.1@aar,androidx.media3:media3-datasource-okhttp:1.4.1@aar,com.squareup.retrofit2:converter-gson:2.9.0@jar,com.squareup.retrofit2:retrofit:2.9.0@jar,com.github.mrmike:ok2curl:0.8.0@jar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1@aar,com.google.mlkit:barcode-scanning-common:17.0.0@aar,com.google.mlkit:vision-common:17.3.0@aar,com.google.mlkit:common:18.11.0@aar,com.google.firebase:firebase-datatransport:19.0.0@aar,com.google.android.datatransport:transport-backend-cct:3.3.0@aar,com.google.firebase:firebase-config-interop:16.0.1@aar,com.google.firebase:firebase-encoders-json:18.0.1@aar,androidx.work:work-runtime-ktx:2.8.1@aar,com.google.android.gms:play-services-ads:24.3.0@aar,com.google.android.gms:play-services-ads-api:24.3.0@aar,androidx.work:work-runtime:2.8.1@aar,androidx.room:room-ktx:2.5.2@aar,androidx.room:room-runtime:2.5.2@aar,androidx.room:room-common:2.5.2@jar,androidx.credentials:credentials:1.2.0-rc01@aar,androidx.credentials:credentials:1.2.0-rc01@aar,androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar,com.google.android.libraries.identity.googleid:googleid:1.1.0@aar,com.airbnb.android:lottie:6.5.2@aar,io.grpc:grpc-okhttp:1.62.2@jar,androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar,androidx.datastore:datastore-core-okio-jvm:1.1.3@jar,androidx.datastore:datastore-core-android:1.1.3@aar,androidx.datastore:datastore-preferences-proto:1.1.3@jar,androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar,androidx.datastore:datastore-preferences-android:1.1.3@aar,androidx.datastore:datastore-android:1.1.3@aar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar,androidx.camera:camera-extensions:1.5.0-alpha03@aar,androidx.camera:camera-video:1.5.0-alpha03@aar,androidx.camera:camera-lifecycle:1.5.0-alpha03@aar,androidx.camera:camera-camera2:1.5.0-alpha03@aar,androidx.camera:camera-core:1.5.0-alpha03@aar,androidx.camera:camera-view:1.5.0-alpha03@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,androidx.constraintlayout:constraintlayout:2.1.4@aar,androidx.appcompat:appcompat:1.7.0@aar,androidx.appcompat:appcompat:1.7.0@aar,com.google.android.gms:play-services-auth:21.3.0@aar,com.google.android.exoplayer:exoplayer:2.19.1@aar,com.google.android.exoplayer:exoplayer-ui:2.19.1@aar,androidx.recyclerview:recyclerview:1.3.1@aar,androidx.viewpager2:viewpager2:1.1.0@aar,com.github.bumptech.glide:avif-integration:4.14.2@aar,com.github.zjupure:webpdecoder:2.6.4.14.2@aar,com.github.bumptech.glide:glide:4.15.1@aar,com.google.android.gms:play-services-location:18.0.0@aar,com.google.android.gms:play-services-appset:16.0.2@aar,com.google.android.gms:play-services-auth-api-phone:18.0.2@aar,com.google.android.gms:play-services-auth-base:18.0.10@aar,com.google.android.gms:play-services-wallet:18.1.3@aar,com.google.android.gms:play-services-measurement:22.4.0@aar,com.google.android.gms:play-services-measurement-sdk:22.4.0@aar,com.google.android.gms:play-services-measurement-impl:22.4.0@aar,com.google.firebase:firebase-appcheck-interop:17.1.0@aar,com.google.firebase:firebase-database-collection:18.0.1@aar,com.google.android.gms:play-services-identity:17.0.0@aar,com.google.android.gms:play-services-maps:17.0.0@aar,com.google.android.gms:play-services-fido:20.1.0@aar,com.google.android.gms:play-services-base:18.5.0@aar,com.google.android.ump:user-messaging-platform:3.2.0@aar,com.google.android.gms:play-services-ads-identifier:18.0.1@aar,com.google.firebase:firebase-auth-interop:20.0.0@aar,com.google.firebase:firebase-common:21.0.0@aar,com.google.firebase:firebase-iid-interop:17.1.0@aar,com.google.android.gms:play-services-cloud-messaging:17.2.0@aar,com.google.android.gms:play-services-stats:17.0.2@aar,com.google.mlkit:vision-interfaces:16.3.0@aar,com.google.android.recaptcha:recaptcha:18.6.1@aar,com.google.android.play:integrity:1.3.0@aar,com.google.firebase:firebase-installations-interop:17.2.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.transition:transition:1.5.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.1.0@aar,androidx.emoji2:emoji2-views-helper:1.3.0@aar,androidx.emoji2:emoji2:1.3.0@aar,com.google.android.exoplayer:exoplayer-hls:2.19.1@aar,com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1@aar,com.google.android.exoplayer:exoplayer-dash:2.19.1@aar,com.google.android.exoplayer:exoplayer-rtsp:2.19.1@aar,com.google.android.exoplayer:exoplayer-core:2.19.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar,androidx.autofill:autofill:1.1.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.2.0-alpha01@aar,androidx.ads:ads-identifier:1.0.0-alpha05@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.webkit:webkit:1.11.0-alpha02@aar,androidx.webkit:webkit:1.11.0-alpha02@aar,androidx.browser:browser:1.8.0@aar,com.facebook.fresco:fresco:3.6.0@aar,com.facebook.fresco:drawee:3.6.0@aar,com.facebook.fresco:nativeimagefilters:3.6.0@aar,com.facebook.fresco:memory-type-native:3.6.0@aar,com.facebook.fresco:memory-type-java:3.6.0@aar,com.facebook.fresco:imagepipeline-native:3.6.0@aar,com.facebook.fresco:memory-type-ashmem:3.6.0@aar,com.facebook.fresco:imagepipeline:3.6.0@aar,com.facebook.fresco:nativeimagetranscoder:3.6.0@aar,com.facebook.fresco:imagepipeline-base:3.6.0@aar,com.facebook.fresco:urimod:3.6.0@aar,com.facebook.fresco:vito-source:3.6.0@aar,com.facebook.fresco:middleware:3.6.0@aar,com.facebook.fresco:ui-common:3.6.0@aar,com.facebook.fresco:soloader:3.6.0@aar,com.facebook.fresco:fbcore:3.6.0@aar,androidx.media:media:1.7.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.core:core:1.16.0@aar,androidx.core:core:1.16.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar,androidx.lifecycle:lifecycle-service:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7@aar,androidx.lifecycle:lifecycle-process:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar,androidx.lifecycle:lifecycle-livedata:2.8.7@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0@jar,androidx.concurrent:concurrent-futures-ktx:1.1.0@jar,androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11@aar,androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11@aar,androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.9.0@jar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.android.gms:play-services-places-placereport:17.0.0@aar,com.google.android.gms:play-services-measurement-sdk-api:22.4.0@aar,com.google.android.gms:play-services-measurement-base:22.4.0@aar,com.google.firebase:firebase-measurement-connector:20.0.1@aar,com.google.android.gms:play-services-basement:18.5.0@aar,androidx.fragment:fragment:1.6.1@aar,androidx.fragment:fragment:1.6.1@aar,androidx.fragment:fragment-ktx:1.6.1@aar,androidx.activity:activity-ktx:1.10.1@aar,androidx.activity:activity:1.10.1@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.core:core-ktx:1.16.0@aar,org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.21@jar,androidx.core:core-viewtree:1.0.0@aar,androidx.annotation:annotation-experimental:1.4.1@aar,com.facebook.fresco:ui-core:3.6.0@aar,androidx.profileinstaller:profileinstaller:1.4.0@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,androidx.tracing:tracing-ktx:1.2.0@aar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.21@jar,com.facebook.react:hermes-android:0.79.2:release@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,com.github.Dimezis:BlurView:version-2.0.4@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.exifinterface:exifinterface:1.4.1@aar,androidx.exifinterface:exifinterface:1.4.1@aar,com.google.android.exoplayer:exoplayer-datasource:2.19.1@aar,com.google.android.exoplayer:exoplayer-database:2.19.1@aar,com.google.android.exoplayer:exoplayer-extractor:2.19.1@aar,com.google.android.exoplayer:exoplayer-decoder:2.19.1@aar,com.google.android.exoplayer:exoplayer-container:2.19.1@aar,com.google.android.exoplayer:exoplayer-common:2.19.1@aar,com.google.firebase:firebase-components:18.0.0@aar,com.google.android.datatransport:transport-runtime:3.3.0@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,androidx.cardview:cardview:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,com.google.firebase:firebase-encoders:17.0.0@jar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,com.github.bumptech.glide:gifdecoder:4.15.1@aar,androidx.ads:ads-identifier-common:1.0.0-alpha05@aar,com.google.android.datatransport:transport-api:3.2.0@aar,androidx.collection:collection-ktx:1.4.2@jar,androidx.collection:collection-jvm:1.4.2@jar,androidx.sqlite:sqlite-framework:2.3.1@aar,androidx.sqlite:sqlite:2.3.1@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.annotation:annotation-jvm:1.8.1@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,org.jetbrains:annotations:23.0.0@jar,app.notifee:core:202108261754@aar,io.grpc:grpc-android:1.62.2@aar,io.grpc:grpc-protobuf-lite:1.62.2@jar,io.grpc:grpc-stub:1.62.2@jar,io.grpc:grpc-util:1.62.2@jar,io.grpc:grpc-core:1.62.2@jar,io.grpc:grpc-context:1.62.2@jar,io.grpc:grpc-api:1.62.2@jar,com.google.guava:guava:33.3.1-android@jar,org.greenrobot:eventbus:3.3.1@aar,io.github.webrtc-sdk:android:125.6422.06.1@aar,org.mp4parser:isoparser:1.9.56@jar,javazoom:jlayer:1.0.1@jar,commons-io:commons-io:2.8.0@jar,org.apache.commons:commons-lang3:3.8@jar,net.time4j:time4j-android:4.8-2021a@aar,com.facebook.yoga:proguard-annotations:1.19.0@jar,com.jakewharton:process-phoenix:2.1.2@aar,com.facebook.fbjni:fbjni:0.7.0@aar,com.facebook.infer.annotation:infer-annotation:0.18.0@jar,com.facebook.soloader:soloader:0.12.1@aar,com.google.code.findbugs:jsr305:3.0.2@jar,com.google.firebase:firebase-annotations:16.2.0@jar,javax.inject:javax.inject:1@jar,com.google.errorprone:error_prone_annotations:2.28.0@jar,org.aomedia.avif.android:avif:0.9.3.a319893@aar,com.google.guava:failureaccess:1.0.2@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,org.checkerframework:checker-qual:3.43.0@jar,com.google.j2objc:j2objc-annotations:3.0.0@jar,org.greenrobot:eventbus-java:3.3.1@jar,org.aspectj:aspectjrt:1.9.7@jar,org.slf4j:slf4j-api:1.8.0-beta4@jar,org.jspecify:jspecify:1.0.0@jar,com.facebook.soloader:nativeloader:0.12.1@jar,com.google.auto.value:auto-value-annotations:1.6.3@jar,com.google.android.odml:image:1.0.0-beta1@aar,org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar,com.facebook.soloader:annotation:0.12.1@jar,com.github.bumptech.glide:disklrucache:4.15.1@jar,com.github.bumptech.glide:annotations:4.15.1@jar,androidx.constraintlayout:constraintlayout-core:1.0.4@jar,com.google.code.gson:gson:2.10.1@jar,com.google.firebase:protolite-well-known-types:18.0.1@aar,com.parse.bolts:bolts-tasks:1.4.0@jar,com.google.android.play:core-common:2.0.3@aar,com.google.protobuf:protobuf-javalite:3.25.5@jar,io.perfmark:perfmark-api:0.26.0@jar,com.google.android:annotations:4.1.1.4@jar,org.codehaus.mojo:animal-sniffer-annotations:1.23@jar">
    <dependency
        name=":@@:react-native-community_datetimepicker::release"
        simpleName="artifacts::react-native-community_datetimepicker"/>
    <dependency
        name=":@@:lottie-react-native::release"
        simpleName="artifacts::lottie-react-native"/>
    <dependency
        name=":@@:react-native-compressor::release"
        simpleName="artifacts::react-native-compressor"/>
    <dependency
        name=":@@:react-native-gesture-handler::release"
        simpleName="artifacts::react-native-gesture-handler"/>
    <dependency
        name=":@@:react-native-google-mobile-ads::release"
        simpleName="artifacts::react-native-google-mobile-ads"/>
    <dependency
        name=":@@:react-native-pager-view::release"
        simpleName="artifacts::react-native-pager-view"/>
    <dependency
        name=":@@:react-native-permissions::release"
        simpleName="artifacts::react-native-permissions"/>
    <dependency
        name=":@@:react-native-safe-area-context::release"
        simpleName="artifacts::react-native-safe-area-context"/>
    <dependency
        name=":@@:react-native-screens::release"
        simpleName="artifacts::react-native-screens"/>
    <dependency
        name=":@@:react-native-video::release"
        simpleName="artifacts::react-native-video"/>
    <dependency
        name=":@@:react-native-vision-camera::release"
        simpleName="artifacts::react-native-vision-camera"/>
    <dependency
        name=":@@:react-native-webview::release"
        simpleName="artifacts::react-native-webview"/>
    <dependency
        name=":@@:d11_react-native-fast-image::release"
        simpleName="artifacts::d11_react-native-fast-image"/>
    <dependency
        name=":@@:notifee_react-native::release"
        simpleName="artifacts::notifee_react-native"/>
    <dependency
        name=":@@:nozbe_watermelondb::release"
        simpleName="artifacts::nozbe_watermelondb"/>
    <dependency
        name=":@@:react-native-async-storage_async-storage::release"
        simpleName="artifacts::react-native-async-storage_async-storage"/>
    <dependency
        name=":@@:react-native-clipboard_clipboard::release"
        simpleName="artifacts::react-native-clipboard_clipboard"/>
    <dependency
        name=":@@:react-native-community_blur::release"
        simpleName="artifacts::react-native-community_blur"/>
    <dependency
        name=":@@:react-native-community_masked-view::release"
        simpleName="artifacts::react-native-community_masked-view"/>
    <dependency
        name=":@@:react-native-community_netinfo::release"
        simpleName="artifacts::react-native-community_netinfo"/>
    <dependency
        name=":@@:react-native-firebase_analytics::release"
        simpleName="artifacts::react-native-firebase_analytics"/>
    <dependency
        name=":@@:react-native-firebase_auth::release"
        simpleName="artifacts::react-native-firebase_auth"/>
    <dependency
        name=":@@:react-native-firebase_crashlytics::release"
        simpleName="artifacts::react-native-firebase_crashlytics"/>
    <dependency
        name=":@@:react-native-firebase_database::release"
        simpleName="artifacts::react-native-firebase_database"/>
    <dependency
        name=":@@:react-native-firebase_firestore::release"
        simpleName="artifacts::react-native-firebase_firestore"/>
    <dependency
        name=":@@:react-native-firebase_messaging::release"
        simpleName="artifacts::react-native-firebase_messaging"/>
    <dependency
        name=":@@:react-native-firebase_storage::release"
        simpleName="artifacts::react-native-firebase_storage"/>
    <dependency
        name=":@@:react-native-firebase_app::release"
        simpleName="artifacts::react-native-firebase_app"/>
    <dependency
        name=":@@:shopify_react-native-skia::release"
        simpleName="artifacts::shopify_react-native-skia"/>
    <dependency
        name=":@@:videosdk.live_react-native-webrtc::release"
        simpleName="artifacts::videosdk.live_react-native-webrtc"/>
    <dependency
        name=":@@:react-native-callkeep::release"
        simpleName="artifacts::react-native-callkeep"/>
    <dependency
        name=":@@:react-native-create-thumbnail::release"
        simpleName="artifacts::react-native-create-thumbnail"/>
    <dependency
        name=":@@:react-native-date-picker::release"
        simpleName="artifacts::react-native-date-picker"/>
    <dependency
        name=":@@:react-native-fs::release"
        simpleName="artifacts::react-native-fs"/>
    <dependency
        name=":@@:react-native-geolocation-service::release"
        simpleName="artifacts::react-native-geolocation-service"/>
    <dependency
        name=":@@:react-native-get-random-values::release"
        simpleName="artifacts::react-native-get-random-values"/>
    <dependency
        name=":@@:react-native-image-crop-picker::release"
        simpleName="artifacts::react-native-image-crop-picker"/>
    <dependency
        name=":@@:react-native-image-picker::release"
        simpleName="artifacts::react-native-image-picker"/>
    <dependency
        name=":@@:react-native-linear-gradient::release"
        simpleName="artifacts::react-native-linear-gradient"/>
    <dependency
        name=":@@:react-native-orientation-locker::release"
        simpleName="artifacts::react-native-orientation-locker"/>
    <dependency
        name=":@@:react-native-razorpay::release"
        simpleName="artifacts::react-native-razorpay"/>
    <dependency
        name=":@@:react-native-reanimated::release"
        simpleName="artifacts::react-native-reanimated"/>
    <dependency
        name=":@@:react-native-restart::release"
        simpleName="artifacts::react-native-restart"/>
    <dependency
        name=":@@:react-native-svg::release"
        simpleName="artifacts::react-native-svg"/>
    <dependency
        name=":@@:react-native-vector-icons::release"
        simpleName="artifacts::react-native-vector-icons"/>
    <dependency
        name=":@@:react-native-view-shot::release"
        simpleName="artifacts::react-native-view-shot"/>
    <dependency
        name=":@@:rnincallmanager::release"
        simpleName="artifacts::rnincallmanager"/>
    <dependency
        name="com.facebook.react:react-android:0.79.2:release@aar"
        simpleName="com.facebook.react:react-android"/>
    <dependency
        name="com.pubscale.sdkone:offerwall:1.0.11@aar"
        simpleName="com.pubscale.sdkone:offerwall"/>
    <dependency
        name="com.google.firebase:firebase-messaging:24.1.1@aar"
        simpleName="com.google.firebase:firebase-messaging"/>
    <dependency
        name="com.github.banketree:AndroidLame-kotlin:v0.0.1@aar"
        simpleName="com.github.banketree:AndroidLame-kotlin"/>
    <dependency
        name="com.razorpay:checkout:1.6.41@aar"
        simpleName="com.razorpay:checkout"/>
    <dependency
        name="com.google.android.material:material:1.12.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="com.razorpay:standard-core:1.6.53@aar"
        simpleName="com.razorpay:standard-core"/>
    <dependency
        name="com.pubscale.caterpillar:analytics:0.23@aar"
        simpleName="com.pubscale.caterpillar:analytics"/>
    <dependency
        name="com.squareup.okhttp3:logging-interceptor:4.10.0@jar"
        simpleName="com.squareup.okhttp3:logging-interceptor"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp-urlconnection"/>
    <dependency
        name="com.google.firebase:firebase-crashlytics-ndk:19.4.4@aar"
        simpleName="com.google.firebase:firebase-crashlytics-ndk"/>
    <dependency
        name="com.google.firebase:firebase-crashlytics:19.4.4@aar"
        simpleName="com.google.firebase:firebase-crashlytics"/>
    <dependency
        name="com.google.firebase:firebase-analytics:22.4.0@aar"
        simpleName="com.google.firebase:firebase-analytics"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-api:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-api"/>
    <dependency
        name="com.google.firebase:firebase-auth:23.2.1@aar"
        simpleName="com.google.firebase:firebase-auth"/>
    <dependency
        name="com.google.firebase:firebase-database:21.0.0@aar"
        simpleName="com.google.firebase:firebase-database"/>
    <dependency
        name="com.google.firebase:firebase-firestore:25.1.4@aar"
        simpleName="com.google.firebase:firebase-firestore"/>
    <dependency
        name="com.google.firebase:firebase-storage:21.0.2@aar"
        simpleName="com.google.firebase:firebase-storage"/>
    <dependency
        name="com.google.firebase:firebase-sessions:2.1.2@aar"
        simpleName="com.google.firebase:firebase-sessions"/>
    <dependency
        name="com.google.firebase:firebase-installations:18.0.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-appcheck:18.0.0@aar"
        simpleName="com.google.firebase:firebase-appcheck"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.github.bumptech.glide:okhttp3-integration:4.14.2@aar"
        simpleName="com.github.bumptech.glide:okhttp3-integration"/>
    <dependency
        name="com.github.yalantis:ucrop:2.2.10@aar"
        simpleName="com.github.yalantis:ucrop"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-okhttp3"/>
    <dependency
        name="androidx.media3:media3-exoplayer-smoothstreaming:1.4.1@aar"
        simpleName="androidx.media3:media3-exoplayer-smoothstreaming"/>
    <dependency
        name="androidx.media3:media3-exoplayer-dash:1.4.1@aar"
        simpleName="androidx.media3:media3-exoplayer-dash"/>
    <dependency
        name="androidx.media3:media3-exoplayer-hls:1.4.1@aar"
        simpleName="androidx.media3:media3-exoplayer-hls"/>
    <dependency
        name="androidx.media3:media3-exoplayer:1.4.1@aar"
        simpleName="androidx.media3:media3-exoplayer"/>
    <dependency
        name="androidx.media3:media3-ui:1.4.1@aar"
        simpleName="androidx.media3:media3-ui"/>
    <dependency
        name="androidx.media3:media3-session:1.4.1@aar"
        simpleName="androidx.media3:media3-session"/>
    <dependency
        name="androidx.media3:media3-datasource:1.4.1@aar"
        simpleName="androidx.media3:media3-datasource"/>
    <dependency
        name="androidx.media3:media3-extractor:1.4.1@aar"
        simpleName="androidx.media3:media3-extractor"/>
    <dependency
        name="androidx.media3:media3-container:1.4.1@aar"
        simpleName="androidx.media3:media3-container"/>
    <dependency
        name="androidx.media3:media3-decoder:1.4.1@aar"
        simpleName="androidx.media3:media3-decoder"/>
    <dependency
        name="androidx.media3:media3-database:1.4.1@aar"
        simpleName="androidx.media3:media3-database"/>
    <dependency
        name="androidx.media3:media3-common:1.4.1@aar"
        simpleName="androidx.media3:media3-common"/>
    <dependency
        name="androidx.media3:media3-datasource-okhttp:1.4.1@aar"
        simpleName="androidx.media3:media3-datasource-okhttp"/>
    <dependency
        name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
        simpleName="com.squareup.retrofit2:converter-gson"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.9.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.github.mrmike:ok2curl:0.8.0@jar"
        simpleName="com.github.mrmike:ok2curl"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1@aar"
        simpleName="com.google.android.gms:play-services-mlkit-barcode-scanning"/>
    <dependency
        name="com.google.mlkit:barcode-scanning-common:17.0.0@aar"
        simpleName="com.google.mlkit:barcode-scanning-common"/>
    <dependency
        name="com.google.mlkit:vision-common:17.3.0@aar"
        simpleName="com.google.mlkit:vision-common"/>
    <dependency
        name="com.google.mlkit:common:18.11.0@aar"
        simpleName="com.google.mlkit:common"/>
    <dependency
        name="com.google.firebase:firebase-datatransport:19.0.0@aar"
        simpleName="com.google.firebase:firebase-datatransport"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.3.0@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.firebase:firebase-config-interop:16.0.1@aar"
        simpleName="com.google.firebase:firebase-config-interop"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.1@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="androidx.work:work-runtime-ktx:2.8.1@aar"
        simpleName="androidx.work:work-runtime-ktx"/>
    <dependency
        name="com.google.android.gms:play-services-ads:24.3.0@aar"
        simpleName="com.google.android.gms:play-services-ads"/>
    <dependency
        name="com.google.android.gms:play-services-ads-api:24.3.0@aar"
        simpleName="com.google.android.gms:play-services-ads-api"/>
    <dependency
        name="androidx.work:work-runtime:2.8.1@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.room:room-ktx:2.5.2@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="androidx.room:room-runtime:2.5.2@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.room:room-common:2.5.2@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.credentials:credentials:1.2.0-rc01@aar"
        simpleName="androidx.credentials:credentials"/>
    <dependency
        name="androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar"
        simpleName="androidx.credentials:credentials-play-services-auth"/>
    <dependency
        name="com.google.android.libraries.identity.googleid:googleid:1.1.0@aar"
        simpleName="com.google.android.libraries.identity.googleid:googleid"/>
    <dependency
        name="com.airbnb.android:lottie:6.5.2@aar"
        simpleName="com.airbnb.android:lottie"/>
    <dependency
        name="io.grpc:grpc-okhttp:1.62.2@jar"
        simpleName="io.grpc:grpc-okhttp"/>
    <dependency
        name="androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar"
        simpleName="androidx.datastore:datastore-preferences-external-protobuf"/>
    <dependency
        name="androidx.datastore:datastore-core-okio-jvm:1.1.3@jar"
        simpleName="androidx.datastore:datastore-core-okio-jvm"/>
    <dependency
        name="androidx.datastore:datastore-core-android:1.1.3@aar"
        simpleName="androidx.datastore:datastore-core-android"/>
    <dependency
        name="androidx.datastore:datastore-preferences-proto:1.1.3@jar"
        simpleName="androidx.datastore:datastore-preferences-proto"/>
    <dependency
        name="androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar"
        simpleName="androidx.datastore:datastore-preferences-core-jvm"/>
    <dependency
        name="androidx.datastore:datastore-preferences-android:1.1.3@aar"
        simpleName="androidx.datastore:datastore-preferences-android"/>
    <dependency
        name="androidx.datastore:datastore-android:1.1.3@aar"
        simpleName="androidx.datastore:datastore-android"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="androidx.camera:camera-extensions:1.5.0-alpha03@aar"
        simpleName="androidx.camera:camera-extensions"/>
    <dependency
        name="androidx.camera:camera-video:1.5.0-alpha03@aar"
        simpleName="androidx.camera:camera-video"/>
    <dependency
        name="androidx.camera:camera-lifecycle:1.5.0-alpha03@aar"
        simpleName="androidx.camera:camera-lifecycle"/>
    <dependency
        name="androidx.camera:camera-camera2:1.5.0-alpha03@aar"
        simpleName="androidx.camera:camera-camera2"/>
    <dependency
        name="androidx.camera:camera-core:1.5.0-alpha03@aar"
        simpleName="androidx.camera:camera-core"/>
    <dependency
        name="androidx.camera:camera-view:1.5.0-alpha03@aar"
        simpleName="androidx.camera:camera-view"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.google.android.gms:play-services-auth:21.3.0@aar"
        simpleName="com.google.android.gms:play-services-auth"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-ui:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-ui"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.3.1@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.1.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="com.github.bumptech.glide:avif-integration:4.14.2@aar"
        simpleName="com.github.bumptech.glide:avif-integration"/>
    <dependency
        name="com.github.zjupure:webpdecoder:2.6.4.14.2@aar"
        simpleName="com.github.zjupure:webpdecoder"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.15.1@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="com.google.android.gms:play-services-location:18.0.0@aar"
        simpleName="com.google.android.gms:play-services-location"/>
    <dependency
        name="com.google.android.gms:play-services-appset:16.0.2@aar"
        simpleName="com.google.android.gms:play-services-appset"/>
    <dependency
        name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-auth-api-phone"/>
    <dependency
        name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
        simpleName="com.google.android.gms:play-services-auth-base"/>
    <dependency
        name="com.google.android.gms:play-services-wallet:18.1.3@aar"
        simpleName="com.google.android.gms:play-services-wallet"/>
    <dependency
        name="com.google.android.gms:play-services-measurement:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-impl:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-impl"/>
    <dependency
        name="com.google.firebase:firebase-appcheck-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-appcheck-interop"/>
    <dependency
        name="com.google.firebase:firebase-database-collection:18.0.1@aar"
        simpleName="com.google.firebase:firebase-database-collection"/>
    <dependency
        name="com.google.android.gms:play-services-identity:17.0.0@aar"
        simpleName="com.google.android.gms:play-services-identity"/>
    <dependency
        name="com.google.android.gms:play-services-maps:17.0.0@aar"
        simpleName="com.google.android.gms:play-services-maps"/>
    <dependency
        name="com.google.android.gms:play-services-fido:20.1.0@aar"
        simpleName="com.google.android.gms:play-services-fido"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.android.ump:user-messaging-platform:3.2.0@aar"
        simpleName="com.google.android.ump:user-messaging-platform"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:18.0.1@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.firebase:firebase-auth-interop:20.0.0@aar"
        simpleName="com.google.firebase:firebase-auth-interop"/>
    <dependency
        name="com.google.firebase:firebase-common:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-iid-interop"/>
    <dependency
        name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
        simpleName="com.google.android.gms:play-services-cloud-messaging"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="com.google.mlkit:vision-interfaces:16.3.0@aar"
        simpleName="com.google.mlkit:vision-interfaces"/>
    <dependency
        name="com.google.android.recaptcha:recaptcha:18.6.1@aar"
        simpleName="com.google.android.recaptcha:recaptcha"/>
    <dependency
        name="com.google.android.play:integrity:1.3.0@aar"
        simpleName="com.google.android.play:integrity"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.2.0@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.transition:transition:1.5.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.1.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-hls:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-hls"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-smoothstreaming"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-dash:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-dash"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-rtsp:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-rtsp"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-core:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-core"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.autofill:autofill:1.1.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.2.0-alpha01@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.ads:ads-identifier:1.0.0-alpha05@aar"
        simpleName="androidx.ads:ads-identifier"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.webkit:webkit:1.11.0-alpha02@aar"
        simpleName="androidx.webkit:webkit"/>
    <dependency
        name="androidx.browser:browser:1.8.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="com.facebook.fresco:fresco:3.6.0@aar"
        simpleName="com.facebook.fresco:fresco"/>
    <dependency
        name="com.facebook.fresco:drawee:3.6.0@aar"
        simpleName="com.facebook.fresco:drawee"/>
    <dependency
        name="com.facebook.fresco:nativeimagefilters:3.6.0@aar"
        simpleName="com.facebook.fresco:nativeimagefilters"/>
    <dependency
        name="com.facebook.fresco:memory-type-native:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-java:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-java"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-native:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-ashmem:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-ashmem"/>
    <dependency
        name="com.facebook.fresco:imagepipeline:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline"/>
    <dependency
        name="com.facebook.fresco:nativeimagetranscoder:3.6.0@aar"
        simpleName="com.facebook.fresco:nativeimagetranscoder"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-base:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-base"/>
    <dependency
        name="com.facebook.fresco:urimod:3.6.0@aar"
        simpleName="com.facebook.fresco:urimod"/>
    <dependency
        name="com.facebook.fresco:vito-source:3.6.0@aar"
        simpleName="com.facebook.fresco:vito-source"/>
    <dependency
        name="com.facebook.fresco:middleware:3.6.0@aar"
        simpleName="com.facebook.fresco:middleware"/>
    <dependency
        name="com.facebook.fresco:ui-common:3.6.0@aar"
        simpleName="com.facebook.fresco:ui-common"/>
    <dependency
        name="com.facebook.fresco:soloader:3.6.0@aar"
        simpleName="com.facebook.fresco:soloader"/>
    <dependency
        name="com.facebook.fresco:fbcore:3.6.0@aar"
        simpleName="com.facebook.fresco:fbcore"/>
    <dependency
        name="androidx.media:media:1.7.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.16.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.concurrent:concurrent-futures-ktx:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures-ktx"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices-java"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.9.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-places-placereport:17.0.0@aar"
        simpleName="com.google.android.gms:play-services-places-placereport"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:20.0.1@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.6.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.6.1@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.activity:activity-ktx:1.10.1@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity:1.10.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.core:core-ktx:1.16.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="com.facebook.fresco:ui-core:3.6.0@aar"
        simpleName="com.facebook.fresco:ui-core"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.tracing:tracing-ktx:1.2.0@aar"
        simpleName="androidx.tracing:tracing-ktx"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="com.facebook.react:hermes-android:0.79.2:release@aar"
        simpleName="com.facebook.react:hermes-android"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="com.github.Dimezis:BlurView:version-2.0.4@aar"
        simpleName="com.github.Dimezis:BlurView"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.4.1@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-datasource:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-datasource"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-database:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-database"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-extractor:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-extractor"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-decoder:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-decoder"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-container:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-container"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-common:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-common"/>
    <dependency
        name="com.google.firebase:firebase-components:18.0.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.3.0@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.15.1@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.ads:ads-identifier-common:1.0.0-alpha05@aar"
        simpleName="androidx.ads:ads-identifier-common"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.2.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="androidx.collection:collection-ktx:1.4.2@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.2@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.3.1@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.3.1@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="app.notifee:core:202108261754@aar"
        simpleName="app.notifee:core"/>
    <dependency
        name="io.grpc:grpc-android:1.62.2@aar"
        simpleName="io.grpc:grpc-android"/>
    <dependency
        name="io.grpc:grpc-protobuf-lite:1.62.2@jar"
        simpleName="io.grpc:grpc-protobuf-lite"/>
    <dependency
        name="io.grpc:grpc-stub:1.62.2@jar"
        simpleName="io.grpc:grpc-stub"/>
    <dependency
        name="io.grpc:grpc-util:1.62.2@jar"
        simpleName="io.grpc:grpc-util"/>
    <dependency
        name="io.grpc:grpc-core:1.62.2@jar"
        simpleName="io.grpc:grpc-core"/>
    <dependency
        name="io.grpc:grpc-context:1.62.2@jar"
        simpleName="io.grpc:grpc-context"/>
    <dependency
        name="io.grpc:grpc-api:1.62.2@jar"
        simpleName="io.grpc:grpc-api"/>
    <dependency
        name="com.google.guava:guava:33.3.1-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="org.greenrobot:eventbus:3.3.1@aar"
        simpleName="org.greenrobot:eventbus"/>
    <dependency
        name="io.github.webrtc-sdk:android:125.6422.06.1@aar"
        simpleName="io.github.webrtc-sdk:android"/>
    <dependency
        name="org.mp4parser:isoparser:1.9.56@jar"
        simpleName="org.mp4parser:isoparser"/>
    <dependency
        name="javazoom:jlayer:1.0.1@jar"
        simpleName="javazoom:jlayer"/>
    <dependency
        name="commons-io:commons-io:2.8.0@jar"
        simpleName="commons-io:commons-io"/>
    <dependency
        name="org.apache.commons:commons-lang3:3.8@jar"
        simpleName="org.apache.commons:commons-lang3"/>
    <dependency
        name="net.time4j:time4j-android:4.8-2021a@aar"
        simpleName="net.time4j:time4j-android"/>
    <dependency
        name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
        simpleName="com.facebook.yoga:proguard-annotations"/>
    <dependency
        name="com.jakewharton:process-phoenix:2.1.2@aar"
        simpleName="com.jakewharton:process-phoenix"/>
    <dependency
        name="com.facebook.fbjni:fbjni:0.7.0@aar"
        simpleName="com.facebook.fbjni:fbjni"/>
    <dependency
        name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
        simpleName="com.facebook.infer.annotation:infer-annotation"/>
    <dependency
        name="com.facebook.soloader:soloader:0.12.1@aar"
        simpleName="com.facebook.soloader:soloader"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.28.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="org.aomedia.avif.android:avif:0.9.3.a319893@aar"
        simpleName="org.aomedia.avif.android:avif"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.2@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="org.checkerframework:checker-qual:3.43.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="com.google.j2objc:j2objc-annotations:3.0.0@jar"
        simpleName="com.google.j2objc:j2objc-annotations"/>
    <dependency
        name="org.greenrobot:eventbus-java:3.3.1@jar"
        simpleName="org.greenrobot:eventbus-java"/>
    <dependency
        name="org.aspectj:aspectjrt:1.9.7@jar"
        simpleName="org.aspectj:aspectjrt"/>
    <dependency
        name="org.slf4j:slf4j-api:1.8.0-beta4@jar"
        simpleName="org.slf4j:slf4j-api"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="com.facebook.soloader:nativeloader:0.12.1@jar"
        simpleName="com.facebook.soloader:nativeloader"/>
    <dependency
        name="com.google.auto.value:auto-value-annotations:1.6.3@jar"
        simpleName="com.google.auto.value:auto-value-annotations"/>
    <dependency
        name="com.google.android.odml:image:1.0.0-beta1@aar"
        simpleName="com.google.android.odml:image"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
        simpleName="org.jetbrains.kotlin:kotlin-annotations-jvm"/>
    <dependency
        name="com.facebook.soloader:annotation:0.12.1@jar"
        simpleName="com.facebook.soloader:annotation"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.15.1@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.15.1@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-core:1.0.4@jar"
        simpleName="androidx.constraintlayout:constraintlayout-core"/>
    <dependency
        name="com.google.code.gson:gson:2.10.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.google.firebase:protolite-well-known-types:18.0.1@aar"
        simpleName="com.google.firebase:protolite-well-known-types"/>
    <dependency
        name="com.parse.bolts:bolts-tasks:1.4.0@jar"
        simpleName="com.parse.bolts:bolts-tasks"/>
    <dependency
        name="com.google.android.play:core-common:2.0.3@aar"
        simpleName="com.google.android.play:core-common"/>
    <dependency
        name="com.google.protobuf:protobuf-javalite:3.25.5@jar"
        simpleName="com.google.protobuf:protobuf-javalite"/>
    <dependency
        name="io.perfmark:perfmark-api:0.26.0@jar"
        simpleName="io.perfmark:perfmark-api"/>
    <dependency
        name="com.google.android:annotations:4.1.1.4@jar"
        simpleName="com.google.android:annotations"/>
    <dependency
        name="org.codehaus.mojo:animal-sniffer-annotations:1.23@jar"
        simpleName="org.codehaus.mojo:animal-sniffer-annotations"/>
  </package>
</dependencies>
