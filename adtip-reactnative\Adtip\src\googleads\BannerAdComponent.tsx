import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import { BannerAd, BannerAdSize, TestIds } from 'react-native-google-mobile-ads';
// import AdRotationService from '../services/AdRotationService';

// Test Ad Unit ID (for development/testing)
const TEST_BANNER_AD_UNIT_ID = TestIds.BANNER; // Official Google test ID for banner ads

// Use only Pubscale ad unit
const PUBSCALE_BANNER_AD_UNIT_ID = '/22387492205,23297313686/com.adtip.app.adtip_app.Banner0.1752230666';

const getBannerAdUnitId = () => {
  if (__DEV__) {
    return TEST_BANNER_AD_UNIT_ID;
  }
  return PUBSCALE_BANNER_AD_UNIT_ID;
};

const BannerAdComponent = () => {
  const [adFailed, setAdFailed] = useState(false);

  const handleAdFailed = (error: any) => {
    console.log('Banner ad failed to load:', error);
    setAdFailed(true);
  };

  const handleAdLoaded = () => {
    console.log('Banner ad loaded successfully');
    setAdFailed(false);
  };

  return (
    <View style={styles.container}>
      <BannerAd
        unitId={getBannerAdUnitId()}
        size={BannerAdSize.BANNER}
        requestOptions={{
          requestNonPersonalizedAdsOnly: false,
          keywords: ['entertainment', 'social', 'communication', 'lifestyle'],
          contentUrl: 'https://adtip.app',
        }}
        onAdLoaded={handleAdLoaded}
        onAdFailedToLoad={handleAdFailed}
        onAdOpened={() => {
          console.log('Banner ad opened');
        }}
        onAdClosed={() => {
          console.log('Banner ad closed');
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginVertical: 8,
  },
});

export default BannerAdComponent; 